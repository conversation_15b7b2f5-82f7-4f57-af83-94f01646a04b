<script lang="ts">
  import { onMount } from "svelte";

  let ws: WebSocket | null = null;
  let ltp: number | null = null;

  onMount(() => {
    ws = new WebSocket("ws://localhost:4000/stream");

    ws.onopen = () => console.log("✅ Connected to backend WS");
    ws.onclose = () => console.log("⚠️ Disconnected from backend WS");

    ws.onmessage = (event) => {
      try {
        const msg = JSON.parse(event.data);

        if (msg.type === "live_feed" && msg.feeds) {
          const firstFeed = Object.values(msg.feeds)[0] as any;
          ltp = firstFeed?.fullFeed?.indexFF?.ltpc?.ltp ?? null;
        }
      } catch (e) {
        console.error("❌ Parse error:", e);
      }
    };

    return () => {
      ws?.close();
    };
  });
</script>

<div class="p-6">
  <h1 class="text-2xl font-bold mb-4">📈 Live Market Data</h1>
  <div class="bg-card p-4 rounded-md border shadow-sm">
    {#if ltp}
      <p class="text-lg font-semibold">LTP: {ltp}</p>
    {:else}
      <p class="text-muted-foreground">Waiting for live data...</p>
    {/if}
  </div>
</div>
