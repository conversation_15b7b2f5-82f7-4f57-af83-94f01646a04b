import { Elysia } from "elysia";
import dotenv from "dotenv";
import { getMarketFeedUrl } from "./ws/upstoxAuth";
import { initProtobuf } from "./ws/protoLoader";
import { connectWebSocket } from "./ws/wsClient";
import { setupGateway } from "./ws/wsGateway";

dotenv.config();

(async () => {
  await initProtobuf();
  const wsUrl = await getMarketFeedUrl(process.env.UPSTOX_ACCESS_TOKEN || "");
  const upstoxWS = await connectWebSocket(wsUrl, ["NSE_INDEX|Nifty Bank"]);

  const app = new Elysia().listen(process.env.PORT || 4000);
  setupGateway(app, upstoxWS);

  console.log(
    `🦊 API + Gateway running at http://localhost:${process.env.PORT || 4000}`,
  );
})();
