import WebSocket from "ws";
import { decodeProtobuf } from "./decoder";

export async function connectWebSocket(wsUrl: string, instruments: string[]) {
  return new Promise<WebSocket>((resolve, reject) => {
    const ws = new WebSocket(wsUrl, { followRedirects: true });

    ws.on("open", () => {
      console.log("✅ Upstox WS connected");
      resolve(ws);

      // Subscribe message
      const data = {
        guid: "guid-" + Date.now(),
        method: "sub",
        data: { mode: "full", instrumentKeys: instruments },
      };
      ws.send(Buffer.from(JSON.stringify(data)));
    });

    ws.on("message", (data: Buffer) => {
      try {
        const decoded = decodeProtobuf(data);
        console.log("📈 Live Data:", JSON.stringify(decoded, null, 2));
      } catch (err) {
        console.error("❌ Decode error:", err);
      }
    });

    ws.on("close", () => console.log("⚠️ Upstox WS closed"));
    ws.on("error", (err: Error) => reject(err));
  });
}
