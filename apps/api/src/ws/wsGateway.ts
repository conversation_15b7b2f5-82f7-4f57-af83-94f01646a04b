import { Elysia } from "elysia";
import WebSocket from "ws";

let clients: any[] = [];

export function setupGateway(app: Elysia, upstoxWS: WebSocket) {
  // WS for frontend
  app.ws("/stream", {
    open(ws) {
      clients.push(ws);
      console.log("Frontend connected");
    },
    close(ws) {
      clients = clients.filter((c) => c !== ws);
    },
  });

  // Forward messages
  upstoxWS.on("message", (data: Buffer) => {
    clients.forEach((c) => c.send(data.toString())); // raw JSON forward
  });
}
