{"name": "advanced_project", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.6.2", "turbo": "^2.5.8", "typescript": "5.9.2"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.23", "workspaces": ["apps/*", "packages/*"], "dependencies": {"tslib": "^2.8.1"}}