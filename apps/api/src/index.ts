import { Elysia } from "elysia";
import { getMarketFeedUrl } from "./ws/upstoxAuth";
import { initProtobuf } from "./ws/protoLoader";
import { connectWebSocket } from "./ws/wsClient";
import { setupGateway } from "./ws/wsGateway";

// Environment variables are loaded by <PERSON><PERSON> automatically

(async () => {
  await initProtobuf();
  const wsUrl = await getMarketFeedUrl(process.env.UPSTOX_ACCESS_TOKEN || "");
  const upstoxWS = await connectWebSocket(wsUrl, ["NSE_INDEX|Nifty Bank"]);

  const app = new Elysia()
    .get("/", () => "API Server Running")
    .get("/test-data", () => {
      // Manually trigger a test data broadcast
      const testData = {
        feeds: {
          "NSE_INDEX|Nifty Bank": {
            fullFeed: {
              indexFF: {
                ltpc: {
                  ltp: 55600 + Math.random() * 100,
                  ltt: Date.now().toString(),
                  cp: 55347.95
                }
              }
            }
          }
        },
        currentTs: Date.now().toString()
      };

      upstoxWS.emit("decoded", testData);
      return { message: "Test data sent", data: testData };
    });

  // Setup WebSocket gateway before starting server
  setupGateway(app, upstoxWS);

  app.listen(process.env.PORT || 4000);

  console.log(
    `🦊 API + Gateway running at http://localhost:${process.env.PORT || 4000}`,
  );
})();
